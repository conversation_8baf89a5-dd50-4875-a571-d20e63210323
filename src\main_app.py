"""
Main application orchestrator for LinkInsta automation
"""
import asyncio
import time
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Any
import signal
import sys

from src.config import Config
from src.utils.logger import logger
from src.message_processor import MessageProcessor
from src.video_generators import TextVideoGenerator, TextImageVideoGenerator, VideoTextGenerator
from src.audio_manager import AudioManager
from src.instagram_client import InstagramClient
from src.youtube_client import YouTubeClient
from src.scheduler import PostScheduler

class LinkInstaApp:
    """Main application class that orchestrates all components"""
    
    def __init__(self):
        self.message_processor = MessageProcessor()
        self.text_video_generator = TextVideoGenerator()
        self.text_image_generator = TextImageVideoGenerator()
        self.video_text_generator = VideoTextGenerator()
        self.audio_manager = AudioManager()
        self.instagram_client = InstagramClient()
        self.youtube_client = YouTubeClient()
        self.scheduler = PostScheduler()
        
        self.is_running = False
        self.poll_interval = Config.POLL_INTERVAL_MINUTES * 60  # Convert to seconds
        
        # Setup signal handlers for graceful shutdown
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
    
    def _signal_handler(self, signum, frame):
        """Handle shutdown signals"""
        logger.info(f"Received signal {signum}, shutting down gracefully...")
        self.stop()
        sys.exit(0)
    
    async def initialize(self) -> bool:
        """
        Initialize the application
        
        Returns:
            True if successful, False otherwise
        """
        try:
            logger.info("Initializing LinkInsta automation app...")
            
            # Validate configuration
            Config.validate_config()
            
            # Create necessary directories
            Config.create_directories()
            
            # Download background music if audio directory is empty
            audio_files = list(Config.AUDIO_DIR.glob("*.mp3")) + list(Config.AUDIO_DIR.glob("*.wav"))
            if len(audio_files) < 5:
                logger.info("Downloading background music...")
                self.audio_manager.download_copyright_free_music(20)
            
            # Test Instagram login (skip only in demo mode)
            if not Config.DEMO_MODE:
                if not self.instagram_client.login():
                    logger.error("Failed to login to Instagram")
                    return False

                if not Config.ENABLE_INSTAGRAM_POSTING:
                    logger.info("Instagram posting disabled (but logged in for testing)")
            else:
                logger.info("Demo mode: Skipping Instagram login completely")
            
            logger.info("Application initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error initializing application: {e}")
            return False
    
    async def process_new_messages(self) -> int:
        """
        Process new messages from Telegram
        
        Returns:
            Number of messages processed
        """
        try:
            logger.info("Processing new messages...")
            
            # Poll for new messages
            new_messages = await self.message_processor.poll_and_process_messages()
            
            if not new_messages:
                logger.info("No new messages to process")
                return 0
            
            processed_count = 0
            
            for message in new_messages:
                try:
                    if message.should_skip:
                        logger.info(f"Skipping message {message.telegram_message.message_id}: V2ray config")
                        continue
                    
                    # Generate video based on content type
                    success = await self._generate_video_for_message(message)
                    
                    if success:
                        processed_count += 1
                        logger.info(f"Successfully processed message {message.telegram_message.message_id}")
                    else:
                        logger.error(f"Failed to process message {message.telegram_message.message_id}")
                
                except Exception as e:
                    logger.error(f"Error processing message {message.telegram_message.message_id}: {e}")
            
            logger.info(f"Processed {processed_count} out of {len(new_messages)} messages")
            return processed_count
            
        except Exception as e:
            logger.error(f"Error processing new messages: {e}")
            return 0
    
    async def _generate_video_for_message(self, message) -> bool:
        """Generate video for a specific message"""
        try:
            content_type = message.content_type
            text_content = message.text_content
            
            # Generate unique output filename
            timestamp = int(time.time())
            output_filename = f"{content_type}_{message.telegram_message.message_id}_{timestamp}.mp4"
            output_path = Config.OUTPUT_DIR / output_filename
            
            success = False
            
            if content_type == 'text_only':
                success = self.text_video_generator.generate_video(text_content, output_path)
            
            elif content_type == 'text_image':
                success = self.text_image_generator.generate_video(
                    text_content, message.media_paths, output_path
                )
            
            elif content_type == 'video_text':
                if message.media_paths:
                    video_path = message.media_paths[0]  # Use first video
                    success = self.video_text_generator.generate_video(
                        text_content, video_path, output_path
                    )
                else:
                    logger.error("No video file found for video_text message")
                    return False
            
            if success:
                # Mark video as generated
                self.message_processor.mark_video_generated(message, output_path)

                # Send video to admin chat for review
                await self._send_video_to_admin_chat(output_path, message)

                return True
            else:
                logger.error(f"Failed to generate video for message {message.telegram_message.message_id}")
                return False
                
        except Exception as e:
            logger.error(f"Error generating video for message: {e}")
            return False

    async def _send_video_to_admin_chat(self, video_path: Path, message) -> bool:
        """Send generated video to admin chat for review"""
        try:
            from src.telegram_client import TelegramClient

            # Create caption with message info
            caption = f"🎥 Generated Video\n\n"
            caption += f"📝 Type: {message.content_type}\n"
            caption += f"🆔 Message ID: {message.telegram_message.message_id}\n"
            caption += f"📄 Text: {message.text_content[:100]}{'...' if len(message.text_content) > 100 else ''}\n"
            caption += f"📁 File: {video_path.name}\n\n"
            caption += f"✅ Ready for Instagram posting"

            # Send to personal chat
            telegram_client = TelegramClient()
            success = await telegram_client.send_video(
                video_path=video_path,
                caption=caption,
                chat_id=Config.TELEGRAM_PERSONAL_CHAT_ID
            )

            if success:
                logger.info(f"✅ Sent video to admin chat: {video_path.name}")
            else:
                logger.error(f"❌ Failed to send video to admin chat: {video_path.name}")

            return success

        except Exception as e:
            logger.error(f"Error sending video to admin chat: {e}")
            return False
    
    def schedule_ready_posts(self) -> int:
        """Schedule posts that are ready for Instagram"""
        try:
            return self.scheduler.schedule_ready_posts()
        except Exception as e:
            logger.error(f"Error scheduling posts: {e}")
            return 0

    def send_schedule_summary(self):
        """Send Instagram schedule summary to admin"""
        try:
            self.scheduler.send_schedule_summary()
        except Exception as e:
            logger.error(f"Error sending schedule summary: {e}")
    
    async def run_cycle(self):
        """Run one complete processing cycle"""
        try:
            logger.info("Starting processing cycle...")

            # Sync video status with actual files (fix disconnect issues)
            self.message_processor.sync_video_status_with_files()

            if Config.INSTANT_POSTING_MODE:
                # Instant posting mode: poll, select best message, create video, and post immediately
                await self.run_instant_posting_cycle()
            else:
                # Original scheduled posting mode
                # Process new messages
                processed_messages = await self.process_new_messages()

                # Ensure hourly posting (simple logic: 1 post per active hour)
                scheduled_posts = self.scheduler.ensure_hourly_posting()

                logger.info(f"Cycle complete: {processed_messages} messages processed, {scheduled_posts} posts scheduled")

            # Clean up old temporary files
            self.message_processor.cleanup_old_temp_files()

            # Clean up old schedule entries (if using scheduler)
            if not Config.INSTANT_POSTING_MODE:
                self.scheduler.cleanup_old_entries()

        except Exception as e:
            logger.error(f"Error in processing cycle: {e}")

    async def run_instant_posting_cycle(self):
        """Run instant posting cycle: poll, select, create video, and post immediately"""
        try:
            logger.info("🚀 Starting instant posting cycle...")

            # Poll for the best message directly from message processor
            processed_messages = await self.message_processor.poll_and_process_messages()

            if not processed_messages:
                logger.info("No messages selected for instant posting")
                return

            # Process only the selected message
            selected_message = processed_messages[0]  # Should only be one in instant mode

            # Generate video for the selected message
            video_generated = await self._generate_video_for_message(selected_message)

            if not video_generated:
                logger.error("Failed to generate video for selected message")
                return

            # Post immediately to platforms (Instagram and YouTube)
            success = await self.post_instantly_to_platforms(selected_message)

            if success:
                logger.info(f"✅ Successfully processed message {selected_message.telegram_message.message_id} instantly")

                # Send video to admin chat if enabled
                if Config.ENABLE_TELEGRAM_VIDEO_SENDING:
                    await self._send_video_to_admin_chat(selected_message.output_video_path, selected_message)
            else:
                logger.error(f"❌ Failed to post message {selected_message.telegram_message.message_id} instantly")

        except Exception as e:
            logger.error(f"Error in instant posting cycle: {e}")

    async def post_instantly_to_platforms(self, processed_message) -> bool:
        """Post a video instantly to Instagram and YouTube"""
        try:
            if not processed_message.output_video_path or not processed_message.output_video_path.exists():
                logger.error("No video file found for instant posting")
                return False

            # Check posting hours if enabled (9 AM - 12 midnight Tehran time)
            if Config.RESPECT_POSTING_HOURS_IN_INSTANT_MODE:
                import pytz
                tehran_tz = pytz.timezone('Asia/Tehran')
                now_tehran = datetime.now(tehran_tz)
                current_hour = now_tehran.hour

                posting_start_hour = int(Config.POSTING_START_HOUR) if hasattr(Config, 'POSTING_START_HOUR') else 9
                posting_end_hour = int(Config.POSTING_END_HOUR) if hasattr(Config, 'POSTING_END_HOUR') else 24

                if not (posting_start_hour <= current_hour < posting_end_hour):
                    logger.info(f"⏰ Outside posting hours: current hour {current_hour}, posting window {posting_start_hour}-{posting_end_hour} (Tehran time)")
                    logger.info(f"📅 Content will be queued for next posting window")

                    # Send notification that content was skipped due to time restrictions
                    await self._send_instant_post_notification(
                        success=True,
                        processed_message=processed_message,
                        media_id=f"Skipped due to posting hours (current: {current_hour}h, window: {posting_start_hour}-{posting_end_hour}h Tehran time)"
                    )

                    # This is not an error - it's intended behavior
                    return True
                else:
                    logger.info(f"✅ Within posting hours: {current_hour} (Tehran time: {now_tehran.strftime('%H:%M')})")

            caption = processed_message.text_content
            video_path = processed_message.output_video_path

            instagram_success = False
            youtube_success = False

            logger.info(f"📱 Posting instantly to platforms: {video_path.name} (Tehran time: {now_tehran.strftime('%H:%M')})")

            # Post to Instagram
            if Config.TEST_MODE:
                # Test mode: simulate posting
                logger.info(f"🧪 TEST MODE: Simulating instant Instagram post")
                logger.info(f"🧪 Video: {video_path}")
                logger.info(f"🧪 Caption: {caption[:100]}{'...' if len(caption) > 100 else ''}")
                instagram_success = True

            elif Config.ENABLE_INSTAGRAM_POSTING:
                # Real Instagram posting
                media_id = self.instagram_client.post_reel(video_path, caption)

                if media_id:
                    logger.info(f"✅ Successfully posted to Instagram: {media_id}")
                    instagram_success = True
                else:
                    logger.error("❌ Instagram posting failed")
            else:
                logger.info("Instagram posting disabled")

            # Post to YouTube
            if Config.TEST_MODE:
                # Test mode: simulate YouTube posting
                logger.info(f"🧪 TEST MODE: Simulating instant YouTube post")
                youtube_success = True

            elif Config.ENABLE_YOUTUBE_POSTING:
                # Real YouTube posting
                video_title = f"LinkInsta Content - {caption[:50]}{'...' if len(caption) > 50 else ''}"
                video_id = self.youtube_client.upload_video(
                    video_path=video_path,
                    title=video_title,
                    description=caption
                )

                if video_id:
                    logger.info(f"✅ Successfully posted to YouTube: {video_id}")
                    logger.info(f"🔗 YouTube URL: https://www.youtube.com/watch?v={video_id}")
                    youtube_success = True
                else:
                    logger.error("❌ YouTube posting failed")
            else:
                logger.info("YouTube posting disabled")

            # Mark as posted if at least one platform succeeded
            if instagram_success or youtube_success:
                if instagram_success:
                    self.message_processor.mark_instagram_posted(processed_message)

                # Send success notification
                platforms = []
                if instagram_success:
                    platforms.append("Instagram")
                if youtube_success:
                    platforms.append("YouTube")

                success_message = f"Posted to: {', '.join(platforms)}"
                await self._send_instant_post_notification(True, processed_message, success_message)
                return True
            else:
                await self._send_instant_post_notification(False, processed_message)
                return False

        except Exception as e:
            logger.error(f"Error posting instantly to platforms: {e}")
            await self._send_instant_post_notification(False, processed_message, error=str(e))
            return False

    async def _send_instant_post_notification(self, success: bool, processed_message, media_id: str = None, error: str = None):
        """Send notification about instant posting result"""
        try:
            from datetime import datetime
            import pytz

            tehran_tz = pytz.timezone('Asia/Tehran')
            now_tehran = datetime.now(tehran_tz)

            if success:
                message = (
                    f"🚀 <b>Instant Instagram Post Successful!</b>\n\n"
                    f"📱 <b>Media ID:</b> {media_id}\n"
                    f"🕐 <b>Posted at:</b> {now_tehran.strftime('%Y-%m-%d %H:%M')} (Tehran)\n"
                    f"📝 <b>Caption:</b> {processed_message.text_content[:150]}{'...' if len(processed_message.text_content) > 150 else ''}\n"
                    f"⚡ <b>Mode:</b> Instant Posting\n\n"
                    f"🔗 @linkychannell"
                )
            else:
                message = (
                    f"❌ <b>Instant Instagram Post Failed!</b>\n\n"
                    f"🕐 <b>Failed at:</b> {now_tehran.strftime('%Y-%m-%d %H:%M')} (Tehran)\n"
                    f"📝 <b>Caption:</b> {processed_message.text_content[:150]}{'...' if len(processed_message.text_content) > 150 else ''}\n"
                    f"⚡ <b>Mode:</b> Instant Posting\n"
                )
                if error:
                    message += f"🔍 <b>Error:</b> {error}\n"

            # Send notification using telegram client
            from src.telegram_client import TelegramClient
            telegram_client = TelegramClient()
            await telegram_client.initialize()
            await telegram_client.send_message(Config.TELEGRAM_PERSONAL_CHAT_ID, message)
            await telegram_client.close()

        except Exception as e:
            logger.error(f"Error sending instant post notification: {e}")
    
    async def run(self):
        """Run the main application loop"""
        try:
            if not await self.initialize():
                logger.error("Failed to initialize application")
                return

            if Config.INSTANT_POSTING_MODE:
                logger.info("🚀 Running in INSTANT POSTING MODE")
                logger.info(f"⏰ Polling every {self.poll_interval/60:.1f} minutes for fresh content")
                # Don't start scheduler in instant posting mode
            else:
                logger.info("📅 Running in SCHEDULED POSTING MODE")
                # Start the post scheduler
                self.scheduler.start_scheduler()
                # Send initial schedule summary to admin
                self.send_schedule_summary()

            self.is_running = True
            logger.info(f"Starting main loop with {self.poll_interval/60:.1f} minute intervals...")

            while self.is_running:
                try:
                    await self.run_cycle()

                    # Wait for next cycle
                    if Config.INSTANT_POSTING_MODE:
                        logger.info(f"⏰ Waiting {self.poll_interval/60:.1f} minutes until next polling cycle...")
                    else:
                        logger.info(f"Waiting {self.poll_interval/60:.1f} minutes until next cycle...")
                    await asyncio.sleep(self.poll_interval)

                except KeyboardInterrupt:
                    logger.info("Received keyboard interrupt, shutting down...")
                    break
                except Exception as e:
                    logger.error(f"Error in main loop: {e}")
                    # Wait a bit before retrying
                    await asyncio.sleep(60)

        except Exception as e:
            logger.error(f"Fatal error in main application: {e}")
        finally:
            self.stop()
    
    def stop(self):
        """Stop the application"""
        logger.info("Stopping LinkInsta application...")

        self.is_running = False

        # Stop scheduler only if not in instant posting mode
        if not Config.INSTANT_POSTING_MODE:
            self.scheduler.stop_scheduler()

        # Logout from Instagram
        self.instagram_client.logout()
        
        logger.info("Application stopped")
    
    def get_status(self) -> Dict[str, Any]:
        """Get current application status"""
        try:
            # Get statistics from various components
            message_stats = self.message_processor.get_statistics()
            audio_stats = self.audio_manager.get_usage_statistics()
            instagram_stats = self.instagram_client.get_post_statistics()
            schedule_stats = self.scheduler.get_schedule_status()
            
            return {
                'is_running': self.is_running,
                'poll_interval_minutes': self.poll_interval / 60,
                'message_processing': message_stats,
                'audio_management': audio_stats,
                'instagram_posting': instagram_stats,
                'scheduling': schedule_stats,
                'last_status_check': time.time()
            }
            
        except Exception as e:
            logger.error(f"Error getting application status: {e}")
            return {'error': str(e)}

# Main entry point
async def main():
    """Main entry point for the application"""
    app = LinkInstaApp()
    await app.run()

if __name__ == "__main__":
    asyncio.run(main())
